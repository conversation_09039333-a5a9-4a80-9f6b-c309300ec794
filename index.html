<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AFK System YML Editor</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-edit"></i> AFK System YML Editor</h1>
        <div class="toolbar">
            <button id="addCard" class="btn btn-primary">
                <i class="fas fa-plus"></i> Přidat Kartu
            </button>
            <button id="generateYml" class="btn btn-success">
                <i class="fas fa-file-code"></i> Generovat YML
            </button>
            <button id="simulate" class="btn btn-warning">
                <i class="fas fa-play"></i> Simulovat
            </button>
            <button id="exportAll" class="btn btn-info">
                <i class="fas fa-download"></i> Export Všech
            </button>
            <button id="importYml" class="btn btn-secondary">
                <i class="fas fa-upload"></i> Import YML
            </button>
            <input type="file" id="fileInput" accept=".yml,.yaml" style="display: none;">
        </div>
    </div>

    <div class="sidebar">
        <h3>Nástroje</h3>
        <div class="tool-section">
            <h4>Šablony</h4>
            <button class="template-btn" data-template="global-level">
                <i class="fas fa-globe"></i> Global Level
            </button>
            <button class="template-btn" data-template="player-level">
                <i class="fas fa-user"></i> Player Level
            </button>
            <button class="template-btn" data-template="rewards">
                <i class="fas fa-gift"></i> Rewards
            </button>
            <button class="template-btn" data-template="permissions">
                <i class="fas fa-key"></i> Permissions
            </button>
        </div>
        
        <div class="tool-section">
            <h4>Barvy Karet</h4>
            <div class="color-palette">
                <div class="color-option" data-color="#e3f2fd" style="background: #e3f2fd;"></div>
                <div class="color-option" data-color="#f3e5f5" style="background: #f3e5f5;"></div>
                <div class="color-option" data-color="#e8f5e8" style="background: #e8f5e8;"></div>
                <div class="color-option" data-color="#fff3e0" style="background: #fff3e0;"></div>
                <div class="color-option" data-color="#ffebee" style="background: #ffebee;"></div>
                <div class="color-option" data-color="#f1f8e9" style="background: #f1f8e9;"></div>
            </div>
        </div>

        <div class="tool-section">
            <h4>Akce</h4>
            <button id="selectAll" class="tool-btn">
                <i class="fas fa-check-square"></i> Vybrat Vše
            </button>
            <button id="deleteSelected" class="tool-btn">
                <i class="fas fa-trash"></i> Smazat Vybrané
            </button>
            <button id="copySelected" class="tool-btn">
                <i class="fas fa-copy"></i> Kopírovat Vybrané
            </button>
        </div>
    </div>

    <div class="main-content">
        <div class="canvas-container">
            <div id="canvas" class="canvas">
                <!-- Karty budou přidány dynamicky -->
            </div>
        </div>
    </div>

    <!-- Modal pro editaci karty -->
    <div id="cardModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Editace Karty</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Obsah bude generován dynamicky -->
            </div>
            <div class="modal-footer">
                <button id="saveCard" class="btn btn-primary">Uložit</button>
                <button id="cancelEdit" class="btn btn-secondary">Zrušit</button>
            </div>
        </div>
    </div>

    <!-- Modal pro YML výstup -->
    <div id="ymlModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>Generovaný YML</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="yml-tabs">
                    <button class="tab-btn active" data-tab="preview">Náhled</button>
                    <button class="tab-btn" data-tab="raw">Raw YML</button>
                </div>
                <div class="tab-content">
                    <div id="ymlPreview" class="tab-pane active">
                        <pre id="ymlOutput"></pre>
                    </div>
                    <div id="ymlRaw" class="tab-pane">
                        <textarea id="ymlTextarea" rows="20"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="downloadYml" class="btn btn-success">
                    <i class="fas fa-download"></i> Stáhnout YML
                </button>
                <button id="copyYml" class="btn btn-info">
                    <i class="fas fa-copy"></i> Kopírovat
                </button>
            </div>
        </div>
    </div>

    <!-- Modal pro simulaci -->
    <div id="simulationModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>Simulace AFK Systému</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="simulation-controls">
                    <div class="control-group">
                        <label>Čas simulace (minuty):</label>
                        <input type="number" id="simTime" value="60" min="1" max="1440">
                    </div>
                    <div class="control-group">
                        <label>Počáteční level:</label>
                        <input type="number" id="startLevel" value="1" min="1">
                    </div>
                    <div class="control-group">
                        <label>Počáteční XP:</label>
                        <input type="number" id="startXP" value="0" min="0">
                    </div>
                    <button id="runSimulation" class="btn btn-primary">
                        <i class="fas fa-play"></i> Spustit Simulaci
                    </button>
                </div>
                <div id="simulationResults" class="simulation-results">
                    <!-- Výsledky simulace -->
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="yml-generator.js"></script>
    <script src="simulator.js"></script>
</body>
</html>

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header h1 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
}

.toolbar {
    display: flex;
    gap: 0.5rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.sidebar {
    position: fixed;
    left: 0;
    top: 80px;
    width: 250px;
    height: calc(100vh - 80px);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    overflow-y: auto;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.tool-section {
    margin-bottom: 2rem;
}

.tool-section h4 {
    color: #34495e;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.template-btn, .tool-btn {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: #ecf0f1;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    text-align: left;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.template-btn:hover, .tool-btn:hover {
    background: #3498db;
    color: white;
    transform: translateX(5px);
}

.color-palette {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.color-option:hover {
    border-color: #3498db;
    transform: scale(1.1);
}

.main-content {
    margin-left: 250px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

.canvas-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    min-height: 600px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.canvas {
    position: relative;
    min-height: 100%;
    background: 
        radial-gradient(circle at 20px 20px, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 60px 60px, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
    background-size: 40px 40px, 80px 80px;
}

.card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    cursor: move;
    transition: all 0.3s ease;
    min-width: 300px;
    max-width: 600px;
    resize: both;
    overflow: auto;
}

.card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card.selected {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #ecf0f1;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.card-controls {
    display: flex;
    gap: 0.25rem;
}

.card-btn {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.card-btn:hover {
    transform: scale(1.1);
}

.btn-edit {
    background: #3498db;
    color: white;
}

.btn-copy {
    background: #17a2b8;
    color: white;
}

.btn-delete {
    background: #e74c3c;
    color: white;
}

.card-content {
    font-size: 0.9rem;
    line-height: 1.4;
}

.level-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    font-size: 0.85rem;
}

.level-table th,
.level-table td {
    padding: 0.4rem;
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: top;
}

.level-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.75rem;
    line-height: 1.2;
}

.level-table th small {
    display: block;
    font-weight: 400;
    color: #6c757d;
    margin-top: 0.2rem;
}

.level-table tr:nth-child(even) {
    background: rgba(0, 0, 0, 0.02);
}

.level-table td {
    font-size: 0.8rem;
}

/* Editační formulář pro úrovně */
.levels-edit-container {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background: #f8f9fa;
}

.level-edit-group {
    background: white;
    transition: box-shadow 0.3s ease;
}

.level-edit-group:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.level-edit-group h5 {
    color: #2c3e50;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #3498db;
}

.calculated-info {
    font-family: 'Courier New', monospace;
}

.calculated-info strong {
    color: #2c3e50;
}

.calculated-info span {
    color: #27ae60;
    font-weight: bold;
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

.config-label {
    font-weight: 500;
    color: #34495e;
}

.config-value {
    background: #ecf0f1;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Modal styly */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-content.large {
    max-width: 900px;
    width: 90%;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #2c3e50;
    margin: 0;
}

.close {
    font-size: 1.5rem;
    cursor: pointer;
    color: #95a5a6;
    transition: color 0.3s ease;
}

.close:hover {
    color: #e74c3c;
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #ecf0f1;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Tabs */
.yml-tabs {
    display: flex;
    border-bottom: 1px solid #ecf0f1;
    margin-bottom: 1rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    border-bottom-color: #3498db;
    color: #3498db;
    font-weight: 600;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

#ymlOutput {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    white-space: pre-wrap;
}

#ymlTextarea {
    width: 100%;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 1rem;
    resize: vertical;
}

/* Simulation styles */
.simulation-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-group label {
    font-weight: 500;
    color: #2c3e50;
}

.control-group input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.simulation-results {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 500;
    color: #2c3e50;
}

.result-value {
    font-family: 'Courier New', monospace;
    color: #27ae60;
    font-weight: 600;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Responzivní design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .header {
        padding: 1rem;
    }

    .toolbar {
        flex-wrap: wrap;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
    }

    .simulation-controls {
        grid-template-columns: 1fr;
    }
}

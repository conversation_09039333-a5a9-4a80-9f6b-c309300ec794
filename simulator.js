class AFKSimulator {
    static simulate(cards, timeMinutes, startLevel = 1, startXP = 0) {
        // Najít Global Level kartu pro simulaci
        const globalCard = cards.find(card => card.template === 'global-level');
        
        if (!globalCard) {
            return {
                error: 'Není k dispozici Global Level karta pro simulaci',
                finalLevel: startLevel,
                finalXP: startXP,
                levelsGained: 0,
                totalXPGained: 0,
                avgXPPerMin: 0
            };
        }
        
        const data = globalCard.data;
        const xpPerMinute = data.afkxpPerMin;
        
        let currentLevel = startLevel;
        let currentXP = startXP;
        let totalXPGained = 0;
        
        // Simulovat každou minutu
        for (let minute = 0; minute < timeMinutes; minute++) {
            // Přidat XP za tuto minutu
            currentXP += xpPerMinute;
            totalXPGained += xpPerMinute;
            
            // Zkontrolovat, zda hr<PERSON><PERSON> dos<PERSON>hl nového levelu
            const levelData = this.findLevelData(data.levels, currentLevel + 1);
            if (levelData && currentXP >= levelData.total) {
                currentLevel++;
                // Pokračovat v kontrole dalších levelů
                minute--; // Znovu zkontrolovat tuto minutu pro případné další levely
            }
        }
        
        const levelsGained = currentLevel - startLevel;
        const avgXPPerMin = totalXPGained / timeMinutes;
        
        return {
            finalLevel: currentLevel,
            finalXP: currentXP,
            levelsGained: levelsGained,
            totalXPGained: totalXPGained,
            avgXPPerMin: avgXPPerMin,
            simulationDetails: this.generateDetailedReport(
                data, timeMinutes, startLevel, startXP, currentLevel, currentXP
            )
        };
    }
    
    static findLevelData(levels, targetLevel) {
        return levels.find(level => level.level === targetLevel);
    }
    
    static generateDetailedReport(data, timeMinutes, startLevel, startXP, finalLevel, finalXP) {
        const report = {
            configuration: {
                xpPerMinute: data.afkxpPerMin,
                bonusPerMinute: data.afkbPerMin,
                multiplier: data.multiplier,
                baseXP: data.baseXP
            },
            simulation: {
                duration: timeMinutes,
                startLevel: startLevel,
                startXP: startXP,
                finalLevel: finalLevel,
                finalXP: finalXP
            },
            levelProgression: [],
            rewards: []
        };
        
        // Vypočítat progres pro každý level
        for (let level = startLevel; level <= finalLevel; level++) {
            const levelData = this.findLevelData(data.levels, level);
            if (levelData) {
                const timeToReach = this.calculateTimeToLevel(data, startLevel, startXP, level);
                
                report.levelProgression.push({
                    level: level,
                    requiredXP: levelData.total,
                    timeToReach: timeToReach,
                    rewards: this.parseRewards(levelData.rewards)
                });
                
                // Přidat odměny
                if (level > startLevel) {
                    report.rewards.push({
                        level: level,
                        rewards: this.parseRewards(levelData.rewards),
                        timeAchieved: timeToReach
                    });
                }
            }
        }
        
        return report;
    }
    
    static calculateTimeToLevel(data, startLevel, startXP, targetLevel) {
        if (targetLevel <= startLevel) return 0;
        
        const targetLevelData = this.findLevelData(data.levels, targetLevel);
        if (!targetLevelData) return -1;
        
        const requiredXP = targetLevelData.total - startXP;
        const timeMinutes = Math.ceil(requiredXP / data.afkxpPerMin);
        
        return timeMinutes;
    }
    
    static parseRewards(rewardsString) {
        const rewards = {
            permissions: [],
            commands: [],
            items: [],
            advancements: []
        };
        
        if (!rewardsString || typeof rewardsString !== 'string') {
            return rewards;
        }
        
        const parts = rewardsString.split(',').map(s => s.trim().toLowerCase());
        
        parts.forEach(part => {
            if (part.includes('permission')) {
                rewards.permissions.push('afk.level.bonus');
            }
            if (part.includes('give')) {
                rewards.commands.push('give player diamond 1');
            }
            if (part.includes('advancement')) {
                rewards.advancements.push('custom:afk_level');
            }
        });
        
        return rewards;
    }
    
    static simulateOptimalPath(cards, targetLevel, maxTime = 1440) {
        const globalCard = cards.find(card => card.template === 'global-level');
        
        if (!globalCard) {
            return { error: 'Není k dispozici Global Level karta' };
        }
        
        const data = globalCard.data;
        const targetLevelData = this.findLevelData(data.levels, targetLevel);
        
        if (!targetLevelData) {
            return { error: `Level ${targetLevel} není definován` };
        }
        
        const requiredXP = targetLevelData.total;
        const timeRequired = Math.ceil(requiredXP / data.afkxpPerMin);
        
        if (timeRequired > maxTime) {
            return {
                achievable: false,
                timeRequired: timeRequired,
                maxTime: maxTime,
                suggestion: `Pro dosažení levelu ${targetLevel} je potřeba ${timeRequired} minut, ale limit je ${maxTime} minut.`
            };
        }
        
        return {
            achievable: true,
            timeRequired: timeRequired,
            targetLevel: targetLevel,
            requiredXP: requiredXP,
            xpPerMinute: data.afkxpPerMin,
            efficiency: (requiredXP / timeRequired).toFixed(2)
        };
    }
    
    static compareConfigurations(cardsArray) {
        const results = [];
        
        cardsArray.forEach((cards, index) => {
            const simulation = this.simulate(cards, 60, 1, 0); // 1 hodina simulace
            results.push({
                configIndex: index,
                result: simulation
            });
        });
        
        // Seřadit podle efektivity
        results.sort((a, b) => b.result.avgXPPerMin - a.result.avgXPPerMin);
        
        return results;
    }
    
    static generatePerformanceReport(cards) {
        const globalCard = cards.find(card => card.template === 'global-level');
        
        if (!globalCard) {
            return { error: 'Není k dispozici Global Level karta' };
        }
        
        const data = globalCard.data;
        const report = {
            configuration: {
                xpPerMinute: data.afkxpPerMin,
                bonusPerMinute: data.afkbPerMin,
                totalLevels: data.levels.length
            },
            performance: {
                timeToMaxLevel: 0,
                averageTimePerLevel: 0,
                totalXPRequired: 0,
                efficiency: 0
            },
            levelAnalysis: []
        };
        
        // Analýza každého levelu
        let totalTime = 0;
        let totalXP = 0;
        
        data.levels.forEach(level => {
            const timeToLevel = Math.ceil(level.total / data.afkxpPerMin);
            totalTime += timeToLevel;
            totalXP += level.total;
            
            report.levelAnalysis.push({
                level: level.level,
                requiredXP: level.total,
                timeToReach: timeToLevel,
                efficiency: (level.total / timeToLevel).toFixed(2)
            });
        });
        
        report.performance.timeToMaxLevel = totalTime;
        report.performance.averageTimePerLevel = (totalTime / data.levels.length).toFixed(2);
        report.performance.totalXPRequired = totalXP;
        report.performance.efficiency = (totalXP / totalTime).toFixed(2);
        
        return report;
    }
    
    static validateConfiguration(cards) {
        const issues = [];
        const warnings = [];
        
        const globalCard = cards.find(card => card.template === 'global-level');
        
        if (!globalCard) {
            issues.push('Chybí Global Level karta');
            return { valid: false, issues, warnings };
        }
        
        const data = globalCard.data;
        
        // Kontrola základních hodnot
        if (data.afkxpPerMin <= 0) {
            issues.push('AFKXP/min musí být větší než 0');
        }
        
        if (data.afkbPerMin < 0) {
            warnings.push('AFKB/min je záporné');
        }
        
        if (data.multiplier <= 0) {
            issues.push('Násobič musí být větší než 0');
        }
        
        if (data.baseXP <= 0) {
            issues.push('Základní XP musí být větší než 0');
        }
        
        // Kontrola levelů
        if (!data.levels || data.levels.length === 0) {
            issues.push('Nejsou definovány žádné levely');
        } else {
            let previousTotal = 0;
            data.levels.forEach((level, index) => {
                if (level.total <= previousTotal) {
                    issues.push(`Level ${level.level}: Celkové XP musí být větší než u předchozího levelu`);
                }
                
                if (level.calculated < 0) {
                    issues.push(`Level ${level.level}: Vypočítaná hodnota nemůže být záporná`);
                }
                
                if (level.custom < 0) {
                    warnings.push(`Level ${level.level}: Vlastní hodnota je záporná`);
                }
                
                previousTotal = level.total;
            });
        }
        
        return {
            valid: issues.length === 0,
            issues,
            warnings
        };
    }
    
    static exportSimulationData(simulationResult, format = 'json') {
        switch (format.toLowerCase()) {
            case 'json':
                return JSON.stringify(simulationResult, null, 2);
            
            case 'csv':
                return this.convertToCSV(simulationResult);
            
            case 'txt':
                return this.convertToText(simulationResult);
            
            default:
                return JSON.stringify(simulationResult, null, 2);
        }
    }
    
    static convertToCSV(data) {
        if (!data.simulationDetails) return '';
        
        let csv = 'Level,Required XP,Time to Reach,Rewards\n';
        
        data.simulationDetails.levelProgression.forEach(level => {
            const rewards = Object.values(level.rewards).flat().join(';');
            csv += `${level.level},${level.requiredXP},${level.timeToReach},"${rewards}"\n`;
        });
        
        return csv;
    }
    
    static convertToText(data) {
        let text = 'AFK System Simulation Report\n';
        text += '================================\n\n';
        
        text += `Final Level: ${data.finalLevel}\n`;
        text += `Final XP: ${data.finalXP}\n`;
        text += `Levels Gained: ${data.levelsGained}\n`;
        text += `Total XP Gained: ${data.totalXPGained}\n`;
        text += `Average XP/min: ${data.avgXPPerMin.toFixed(2)}\n\n`;
        
        if (data.simulationDetails) {
            text += 'Level Progression:\n';
            text += '------------------\n';
            
            data.simulationDetails.levelProgression.forEach(level => {
                text += `Level ${level.level}: ${level.requiredXP} XP (${level.timeToReach} min)\n`;
            });
        }
        
        return text;
    }
}

// Export pro použití v jiných souborech
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AFKSimulator;
}

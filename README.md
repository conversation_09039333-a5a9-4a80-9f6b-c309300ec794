# AFK System YML Editor

Profesionální editor pro vytváření .yml konfiguračních souborů pro AFK systém Minecraft serveru.

## Funkce

### 🎨 Canvas Editor
- **Drag & Drop karty** - Přesouván<PERSON> karet po plátně
- **Změna velikosti** - <PERSON><PERSON><PERSON> lze zvětšovat a zmenšovat
- **Změna bar<PERSON>** - Paleta barev pro rozlišení typů karet
- **Výběr více karet** - Ctrl+klik pro výběr více karet najednou
- **Kopírování a mazání** - Rychlé duplikování a odstraňování karet

### 📋 Typy Karet

#### Global Level
- Nastavení AFKB/min a AFKXP/min
- Násobič pro další level
- Základní hodnota AFKXP
- Tabulka úrovní s vypočítanými hodnotami
- Odměny pro každý level

#### Player Level
- Specifické nastavení pro hráče
- XP multiplikátory
- Bonus odměny

#### Rewards
- Systém odměn
- Permissions, př<PERSON>azy, předměty

#### Permissions
- Nastavení oprávnění
- Skupiny a uzly

### 🔧 Nástroje

#### Generování YML
- Automatické generování .yml souborů
- Náhled v editoru
- Stažení souborů
- Kopírování do schránky

#### Simulace
- Testování konfigurace
- Simulace času AFK
- Výpočet získaných levelů
- Analýza efektivity

#### Export/Import
- Export všech karet do JSON
- Import YML souborů
- Generování Minecraft datapacků
- Generování plugin konfigurací

## Použití

### Spuštění
1. Otevřete `index.html` v prohlížeči
2. Editor se automaticky načte s ukázkovou kartou

### Vytvoření nové karty
1. Klikněte na "Přidat Kartu" nebo vyberte šablonu ze sidebaru
2. Karta se objeví na plátně
3. Dvojklik na kartu pro editaci

### Editace karty
1. Klikněte na ikonu editace (tužka) na kartě
2. Upravte hodnoty v modálním okně
3. Klikněte "Uložit" pro potvrzení změn

### Generování YML
1. Klikněte na "Generovat YML"
2. Zkontrolujte náhled
3. Stáhněte nebo zkopírujte YML soubor

### Simulace
1. Klikněte na "Simulovat"
2. Nastavte parametry simulace
3. Spusťte simulaci pro zobrazení výsledků

## Klávesové zkratky

- `Ctrl + A` - Vybrat všechny karty
- `Ctrl + C` - Kopírovat vybrané karty
- `Delete` - Smazat vybrané karty

## Struktura YML

Generovaný YML soubor obsahuje:

```yaml
afk-system:
  afk-detection-time: 60
  afk-bonus-per-minute: 1
  afk-xp-per-minute: 1
  
  levels:
    multiplier: 2
    base-xp: 100
    
    level-definitions:
      1:
        required-xp: 200
        calculated-xp: 100
        custom-bonus: 100
        rewards:
          permissions:
            - "afk.level.1"
          commands:
            - "give {player} diamond 1"
```

## Minecraft Integrace

### Datapack
Editor může generovat Minecraft datapack s:
- Funkcemi pro každý level
- Advancement systémem
- Loot tabulkami

### Plugin
Generování konfigurace pro Bukkit/Spigot plugin:
- plugin.yml
- config.yml
- Permissions setup

## Technické detaily

### Soubory
- `index.html` - Hlavní HTML struktura
- `style.css` - CSS styly a responzivní design
- `script.js` - Hlavní logika editoru
- `yml-generator.js` - Generování YML souborů
- `simulator.js` - Simulační engine

### Závislosti
- Font Awesome - Ikony
- Vanilla JavaScript - Bez externích knihoven

### Kompatibilita
- Moderní prohlížeče (Chrome, Firefox, Safari, Edge)
- Responzivní design pro mobilní zařízení

## Rozšíření

Editor je navržen modulárně pro snadné přidání nových funkcí:

1. **Nové typy karet** - Přidání do `templates` objektu
2. **Nové generátory** - Rozšíření `YMLGenerator` třídy
3. **Nové simulace** - Přidání do `AFKSimulator` třídy

## Podpora

Pro hlášení chyb nebo návrhy na vylepšení vytvořte issue v repository.

## Licence

MIT License - Volně použitelné pro komerční i nekomerční účely.

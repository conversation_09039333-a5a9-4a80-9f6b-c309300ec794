class YMLGenerator {
    static generateFromCards(cards) {
        let yml = '';
        
        // Hlavička YML souboru
        yml += '# AFK System Configuration\n';
        yml += '# Generated by AFK System YML Editor\n';
        yml += `# Generated at: ${new Date().toISOString()}\n\n`;
        
        // Najít Global Level kartu
        const globalCard = cards.find(card => card.template === 'global-level');
        
        if (globalCard) {
            yml += this.generateGlobalLevelYML(globalCard);
        }
        
        // Přidat další typy karet
        const otherCards = cards.filter(card => card.template !== 'global-level');
        otherCards.forEach(card => {
            yml += this.generateCardYML(card);
        });
        
        return yml;
    }
    
    static generateGlobalLevelYML(card) {
        const data = card.data;
        let yml = '';
        
        yml += 'afk-system:\n';
        yml += '  # Základní nastavení\n';
        yml += `  afk-detection-time: 60 # sekund\n`;
        yml += `  afk-bonus-per-minute: ${data.afkbPerMin}\n`;
        yml += `  afk-xp-per-minute: ${data.afkxpPerMin}\n`;
        yml += '\n';
        
        yml += '  # Nastavení levelů\n';
        yml += '  levels:\n';
        yml += `    multiplier: ${data.multiplier}\n`;
        yml += `    base-xp: ${data.baseXP}\n`;
        yml += '    \n';
        
        yml += '    # Definice jednotlivých levelů\n';
        yml += '    level-definitions:\n';
        
        data.levels.forEach(level => {
            yml += `      ${level.level}:\n`;
            yml += `        # Výpočet: ${data.baseXP} × ${level.level} × ${data.multiplier} + ${level.custom} = ${level.total}\n`;
            yml += `        required-xp: ${level.total}\n`;
            yml += `        calculated-xp: ${level.calculated}\n`;
            yml += `        custom-bonus: ${level.custom}\n`;
            yml += `        base-calculation: ${data.baseXP * level.level * data.multiplier}\n`;

            // Parsovat odměny
            const rewards = this.parseRewards(level.rewards);
            let hasRewards = false;

            if (rewards.permissions.length > 0 || rewards.commands.length > 0 || rewards.items.length > 0) {
                yml += '        rewards:\n';
                hasRewards = true;
            }

            if (rewards.permissions.length > 0) {
                yml += '          permissions:\n';
                rewards.permissions.forEach(perm => {
                    yml += `            - "${perm}"\n`;
                });
            }

            if (rewards.commands.length > 0) {
                yml += '          commands:\n';
                rewards.commands.forEach(cmd => {
                    yml += `            - "${cmd}"\n`;
                });
            }

            if (rewards.items.length > 0) {
                yml += '          items:\n';
                rewards.items.forEach(item => {
                    yml += `            - ${item}\n`;
                });
            }

            // Přidat raw odměny jako string pro custom zpracování
            if (level.rewards && level.rewards.trim()) {
                yml += `        raw-rewards: "${level.rewards}"\n`;
            }

            if (level.notes && level.notes.trim()) {
                yml += `        notes: "${level.notes}"\n`;
            }

            yml += '\n';
        });
        
        return yml;
    }
    
    static generateCardYML(card) {
        let yml = '';
        
        switch (card.template) {
            case 'player-level':
                yml += this.generatePlayerLevelYML(card);
                break;
            case 'rewards':
                yml += this.generateRewardsYML(card);
                break;
            case 'permissions':
                yml += this.generatePermissionsYML(card);
                break;
            default:
                yml += `# ${card.title} - Custom configuration\n`;
                yml += `${card.template}:\n`;
                yml += `  # Add custom configuration here\n\n`;
        }
        
        return yml;
    }
    
    static generatePlayerLevelYML(card) {
        const data = card.data;
        let yml = '';
        
        yml += '  # Nastavení specifické pro hráče\n';
        yml += '  player-settings:\n';
        yml += `    player-specific: ${data.playerSpecific || false}\n`;
        yml += `    xp-multiplier: ${data.xpMultiplier || 1.0}\n`;
        
        if (data.bonusRewards && data.bonusRewards.length > 0) {
            yml += '    bonus-rewards:\n';
            data.bonusRewards.forEach(reward => {
                yml += `      - "${reward}"\n`;
            });
        }
        
        yml += '\n';
        return yml;
    }
    
    static generateRewardsYML(card) {
        const data = card.data;
        let yml = '';
        
        yml += '  # Systém odměn\n';
        yml += '  rewards:\n';
        
        if (data.permissions && data.permissions.length > 0) {
            yml += '    permissions:\n';
            data.permissions.forEach(perm => {
                yml += `      - "${perm}"\n`;
            });
        }
        
        if (data.commands && data.commands.length > 0) {
            yml += '    commands:\n';
            data.commands.forEach(cmd => {
                yml += `      - "${cmd}"\n`;
            });
        }
        
        if (data.items && data.items.length > 0) {
            yml += '    items:\n';
            data.items.forEach(item => {
                yml += `      - ${item}\n`;
            });
        }
        
        yml += '\n';
        return yml;
    }
    
    static generatePermissionsYML(card) {
        const data = card.data;
        let yml = '';
        
        yml += '  # Nastavení oprávnění\n';
        yml += '  permissions:\n';
        
        if (data.groups && data.groups.length > 0) {
            yml += '    groups:\n';
            data.groups.forEach(group => {
                yml += `      - "${group}"\n`;
            });
        }
        
        if (data.nodes && data.nodes.length > 0) {
            yml += '    nodes:\n';
            data.nodes.forEach(node => {
                yml += `      - "${node}"\n`;
            });
        }
        
        yml += '\n';
        return yml;
    }
    
    static parseRewards(rewardsString) {
        const rewards = {
            permissions: [],
            commands: [],
            items: []
        };
        
        if (!rewardsString || typeof rewardsString !== 'string') {
            return rewards;
        }
        
        // Jednoduchý parser pro odměny
        const parts = rewardsString.split(',').map(s => s.trim());
        
        parts.forEach(part => {
            if (part.toLowerCase().includes('permission')) {
                rewards.permissions.push('afk.level.' + Math.floor(Math.random() * 100));
            } else if (part.toLowerCase().includes('give')) {
                rewards.commands.push('give {player} diamond 1');
            } else if (part.toLowerCase().includes('advancement')) {
                rewards.commands.push('advancement grant {player} only custom:afk_level');
            }
        });
        
        return rewards;
    }
    
    static generateMinecraftDatapack(cards) {
        const datapack = {
            'pack.mcmeta': {
                pack: {
                    pack_format: 10,
                    description: 'AFK System Datapack - Generated by AFK Editor'
                }
            },
            'data/afk/functions/': {},
            'data/afk/advancements/': {},
            'data/afk/loot_tables/': {}
        };
        
        // Generovat funkce pro každý level
        const globalCard = cards.find(card => card.template === 'global-level');
        if (globalCard) {
            globalCard.data.levels.forEach(level => {
                const functionName = `level_${level.level}_reward.mcfunction`;
                let commands = [];
                
                // Přidat XP
                commands.push(`experience add @s ${level.total} points`);
                
                // Přidat odměny
                const rewards = this.parseRewards(level.rewards);
                rewards.commands.forEach(cmd => {
                    commands.push(cmd.replace('{player}', '@s'));
                });
                
                // Zpráva hráči
                commands.push(`tellraw @s {"text":"Dosáhl jsi AFK Level ${level.level}!","color":"gold"}`);
                
                datapack['data/afk/functions/'][functionName] = commands.join('\n');
            });
        }
        
        return datapack;
    }
    
    static generatePluginConfig(cards) {
        const config = {
            'plugin.yml': {
                name: 'AFKSystem',
                version: '1.0.0',
                main: 'com.example.afksystem.AFKSystem',
                author: 'AFK Editor',
                description: 'AFK System plugin generated by AFK Editor',
                commands: {
                    afk: {
                        description: 'AFK system commands',
                        usage: '/afk [player]'
                    }
                },
                permissions: {
                    'afk.use': {
                        description: 'Allows use of AFK system',
                        default: true
                    },
                    'afk.admin': {
                        description: 'Allows administration of AFK system',
                        default: 'op'
                    }
                }
            },
            'config.yml': this.generateFromCards(cards)
        };
        
        return config;
    }
}

// Export pro použití v jiných souborech
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YMLGenerator;
}

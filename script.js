class AFKEditor {
    constructor() {
        this.cards = [];
        this.selectedCards = [];
        this.draggedCard = null;
        this.dragOffset = { x: 0, y: 0 };
        this.cardCounter = 0;
        this.currentEditCard = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadTemplates();
        
        // Přidat první kartu jako příklad
        this.addCard('global-level', { x: 50, y: 50 });
    }

    setupEventListeners() {
        // Toolbar buttons
        document.getElementById('addCard').addEventListener('click', () => {
            this.showTemplateSelector();
        });
        
        document.getElementById('generateYml').addEventListener('click', () => {
            this.generateYML();
        });
        
        document.getElementById('simulate').addEventListener('click', () => {
            this.showSimulation();
        });
        
        document.getElementById('exportAll').addEventListener('click', () => {
            this.exportAll();
        });
        
        document.getElementById('importYml').addEventListener('click', () => {
            document.getElementById('fileInput').click();
        });
        
        document.getElementById('fileInput').addEventListener('change', (e) => {
            this.importYML(e.target.files[0]);
        });

        // Template buttons
        document.querySelectorAll('.template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const template = e.target.dataset.template;
                this.addCard(template);
            });
        });

        // Color palette
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', (e) => {
                const color = e.target.dataset.color;
                this.changeSelectedCardsColor(color);
            });
        });

        // Tool buttons
        document.getElementById('selectAll').addEventListener('click', () => {
            this.selectAllCards();
        });
        
        document.getElementById('deleteSelected').addEventListener('click', () => {
            this.deleteSelectedCards();
        });
        
        document.getElementById('copySelected').addEventListener('click', () => {
            this.copySelectedCards();
        });

        // Canvas events
        const canvas = document.getElementById('canvas');
        canvas.addEventListener('click', (e) => {
            if (e.target === canvas) {
                this.clearSelection();
            }
        });

        // Modal events
        this.setupModalEvents();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'a':
                        e.preventDefault();
                        this.selectAllCards();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.copySelectedCards();
                        break;
                    case 'Delete':
                        e.preventDefault();
                        this.deleteSelectedCards();
                        break;
                }
            }
        });
    }

    setupModalEvents() {
        // Close modals
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                modal.style.display = 'none';
            });
        });

        // Click outside modal to close
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // Card modal events
        document.getElementById('saveCard').addEventListener('click', () => {
            this.saveCardEdit();
        });
        
        document.getElementById('cancelEdit').addEventListener('click', () => {
            document.getElementById('cardModal').style.display = 'none';
        });

        // YML modal events
        document.getElementById('downloadYml').addEventListener('click', () => {
            this.downloadYML();
        });
        
        document.getElementById('copyYml').addEventListener('click', () => {
            this.copyYMLToClipboard();
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchTab(tabName);
            });
        });

        // Simulation events
        document.getElementById('runSimulation').addEventListener('click', () => {
            this.runSimulation();
        });
    }

    loadTemplates() {
        this.templates = {
            'global-level': {
                title: 'Global Level',
                color: '#e3f2fd',
                data: {
                    afkbPerMin: 1,
                    afkxpPerMin: 1,
                    multiplier: 2,
                    baseXP: 100,
                    levels: [
                        { level: 1, calculated: 200, custom: 0, total: 200, rewards: 'permission, give, advancement', notes: '', time: 100 },
                        { level: 2, calculated: 400, custom: 0, total: 400, rewards: 'permission, give, advancement', notes: '', time: 200 },
                        { level: 3, calculated: 600, custom: 0, total: 600, rewards: 'permission, give, advancement', notes: '', time: 300 },
                        { level: 4, calculated: 800, custom: 0, total: 800, rewards: 'permission, give, advancement', notes: '', time: 400 },
                        { level: 5, calculated: 1000, custom: 0, total: 1000, rewards: 'permission, give, advancement', notes: '', time: 500 },
                        { level: 6, calculated: 1200, custom: 0, total: 1200, rewards: 'permission, give, advancement', notes: '', time: 600 }
                    ]
                }
            },
            'player-level': {
                title: 'Player Level',
                color: '#f3e5f5',
                data: {
                    playerSpecific: true,
                    xpMultiplier: 1.5,
                    bonusRewards: []
                }
            },
            'rewards': {
                title: 'Rewards',
                color: '#e8f5e8',
                data: {
                    permissions: [],
                    commands: [],
                    items: []
                }
            },
            'permissions': {
                title: 'Permissions',
                color: '#fff3e0',
                data: {
                    groups: [],
                    nodes: []
                }
            }
        };
    }

    addCard(templateName, position = null) {
        const template = this.templates[templateName];
        if (!template) return;

        const card = {
            id: `card_${++this.cardCounter}`,
            template: templateName,
            title: template.title,
            color: template.color,
            data: JSON.parse(JSON.stringify(template.data)),
            position: position || this.getRandomPosition(),
            size: { width: 400, height: 300 }
        };

        this.cards.push(card);
        this.renderCard(card);
    }

    getRandomPosition() {
        const canvas = document.getElementById('canvas');
        const rect = canvas.getBoundingClientRect();
        return {
            x: Math.random() * (rect.width - 400),
            y: Math.random() * (rect.height - 300)
        };
    }

    renderCard(card) {
        const cardElement = document.createElement('div');
        cardElement.className = 'card';
        cardElement.id = card.id;
        cardElement.style.left = card.position.x + 'px';
        cardElement.style.top = card.position.y + 'px';
        cardElement.style.width = card.size.width + 'px';
        cardElement.style.height = card.size.height + 'px';
        cardElement.style.backgroundColor = card.color;

        cardElement.innerHTML = this.generateCardHTML(card);
        
        this.setupCardEvents(cardElement, card);
        
        document.getElementById('canvas').appendChild(cardElement);
    }

    generateCardHTML(card) {
        let content = '';
        
        if (card.template === 'global-level') {
            content = this.generateGlobalLevelHTML(card.data);
        } else {
            content = `<div class="config-item">
                <span class="config-label">Template:</span>
                <span class="config-value">${card.template}</span>
            </div>`;
        }

        return `
            <div class="card-header">
                <div class="card-title">${card.title}</div>
                <div class="card-controls">
                    <button class="card-btn btn-edit" title="Editovat">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="card-btn btn-copy" title="Kopírovat">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="card-btn btn-delete" title="Smazat">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-content">
                ${content}
            </div>
        `;
    }

    generateGlobalLevelHTML(data) {
        return `
            <div class="config-item">
                <span class="config-label">AFKB/min:</span>
                <span class="config-value">${data.afkbPerMin}</span>
            </div>
            <div class="config-item">
                <span class="config-label">AFKXP/min:</span>
                <span class="config-value">${data.afkxpPerMin}</span>
            </div>
            <div class="config-item">
                <span class="config-label">Násobič pro další lvl:</span>
                <span class="config-value">${data.multiplier}</span>
            </div>
            <div class="config-item">
                <span class="config-label">Základní hodnota AFKXP pro LVL:</span>
                <span class="config-value">${data.baseXP}</span>
            </div>
            <div class="config-item">
                <span class="config-label">Výpočet:</span>
                <span class="config-value">základní × úroveň × násobič + přidaná</span>
            </div>
            <table class="level-table">
                <thead>
                    <tr>
                        <th>Úroveň</th>
                        <th>Vypočítaná<br><small>(${data.baseXP}×lvl×${data.multiplier})</small></th>
                        <th>Přidaná<br><small>(editovatelná)</small></th>
                        <th>Celkem<br><small>(vypoč.+přid.)</small></th>
                        <th>Odměny</th>
                        <th>Poznámky</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.levels.map(level => `
                        <tr>
                            <td><strong>${level.level}</strong></td>
                            <td>${level.calculated}</td>
                            <td><span style="color: #e74c3c; font-weight: bold;">${level.custom}</span></td>
                            <td><strong>${level.total}</strong></td>
                            <td style="font-size: 0.8rem; max-width: 120px; word-wrap: break-word;">${level.rewards}</td>
                            <td style="font-size: 0.8rem; max-width: 100px; word-wrap: break-word;">${level.notes || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    setupCardEvents(cardElement, card) {
        // Drag and drop
        cardElement.addEventListener('mousedown', (e) => {
            if (e.target.closest('.card-btn')) return;
            
            this.startDrag(cardElement, card, e);
        });

        // Selection
        cardElement.addEventListener('click', (e) => {
            if (e.target.closest('.card-btn')) return;
            
            e.stopPropagation();
            this.toggleCardSelection(card);
        });

        // Card buttons
        const editBtn = cardElement.querySelector('.btn-edit');
        const copyBtn = cardElement.querySelector('.btn-copy');
        const deleteBtn = cardElement.querySelector('.btn-delete');

        editBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.editCard(card);
        });

        copyBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.copyCard(card);
        });

        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.deleteCard(card);
        });
    }

    startDrag(cardElement, card, e) {
        this.draggedCard = card;
        this.dragOffset = {
            x: e.clientX - card.position.x,
            y: e.clientY - card.position.y
        };

        const onMouseMove = (e) => {
            if (this.draggedCard) {
                const newX = e.clientX - this.dragOffset.x;
                const newY = e.clientY - this.dragOffset.y;
                
                card.position.x = Math.max(0, newX);
                card.position.y = Math.max(0, newY);
                
                cardElement.style.left = card.position.x + 'px';
                cardElement.style.top = card.position.y + 'px';
            }
        };

        const onMouseUp = () => {
            this.draggedCard = null;
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        };

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    }

    toggleCardSelection(card) {
        const index = this.selectedCards.indexOf(card);
        const cardElement = document.getElementById(card.id);
        
        if (index === -1) {
            this.selectedCards.push(card);
            cardElement.classList.add('selected');
        } else {
            this.selectedCards.splice(index, 1);
            cardElement.classList.remove('selected');
        }
    }

    clearSelection() {
        this.selectedCards.forEach(card => {
            const cardElement = document.getElementById(card.id);
            cardElement.classList.remove('selected');
        });
        this.selectedCards = [];
    }

    selectAllCards() {
        this.clearSelection();
        this.cards.forEach(card => {
            this.selectedCards.push(card);
            const cardElement = document.getElementById(card.id);
            cardElement.classList.add('selected');
        });
    }

    changeSelectedCardsColor(color) {
        this.selectedCards.forEach(card => {
            card.color = color;
            const cardElement = document.getElementById(card.id);
            cardElement.style.backgroundColor = color;
        });
    }

    copyCard(card) {
        const newCard = JSON.parse(JSON.stringify(card));
        newCard.id = `card_${++this.cardCounter}`;
        newCard.position = {
            x: card.position.x + 20,
            y: card.position.y + 20
        };
        
        this.cards.push(newCard);
        this.renderCard(newCard);
    }

    copySelectedCards() {
        this.selectedCards.forEach(card => {
            this.copyCard(card);
        });
    }

    deleteCard(card) {
        const index = this.cards.indexOf(card);
        if (index !== -1) {
            this.cards.splice(index, 1);
            const cardElement = document.getElementById(card.id);
            cardElement.remove();
            
            const selectedIndex = this.selectedCards.indexOf(card);
            if (selectedIndex !== -1) {
                this.selectedCards.splice(selectedIndex, 1);
            }
        }
    }

    deleteSelectedCards() {
        [...this.selectedCards].forEach(card => {
            this.deleteCard(card);
        });
    }

    editCard(card) {
        this.currentEditCard = card;
        this.showEditModal(card);
    }

    showEditModal(card) {
        const modal = document.getElementById('cardModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        modalTitle.textContent = `Editace: ${card.title}`;
        modalBody.innerHTML = this.generateEditForm(card);

        // Přidat event listenery pro real-time výpočty
        if (card.template === 'global-level') {
            this.setupLevelCalculationEvents(card.data);
        }

        modal.style.display = 'block';
    }

    setupLevelCalculationEvents(data) {
        // Event listener pro přegenerování úrovní
        const regenerateBtn = document.getElementById('regenerateLevels');
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => {
                this.regenerateLevels(data);
            });
        }

        // Event listener pro přepočítání všech úrovní
        const recalculateBtn = document.getElementById('recalculateAll');
        if (recalculateBtn) {
            recalculateBtn.addEventListener('click', () => {
                this.recalculateAllLevels(data);
            });
        }

        // Event listenery pro změny v základních hodnotách
        ['edit_baseXP', 'edit_multiplier'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', () => {
                    this.recalculateAllLevels(data);
                });
            }
        });

        // Event listenery pro změny v přidaných hodnotách
        data.levels.forEach((level, index) => {
            const customInput = document.getElementById(`edit_level_${index}_custom`);
            if (customInput) {
                customInput.addEventListener('input', () => {
                    this.recalculateLevel(data, index);
                });
            }
        });
    }

    regenerateLevels(data) {
        const levelCount = parseInt(document.getElementById('edit_levelCount').value);
        const baseXP = parseInt(document.getElementById('edit_baseXP').value) || data.baseXP;
        const multiplier = parseFloat(document.getElementById('edit_multiplier').value) || data.multiplier;

        // Vytvořit nové úrovně
        const newLevels = [];
        for (let i = 1; i <= levelCount; i++) {
            const calculated = baseXP * i * multiplier;
            newLevels.push({
                level: i,
                calculated: calculated,
                custom: 0,
                total: calculated,
                rewards: 'permission, give, advancement',
                notes: '',
                time: calculated
            });
        }

        data.levels = newLevels;

        // Znovu vygenerovat formulář
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = this.generateEditForm({ template: 'global-level', data: data });
        this.setupLevelCalculationEvents(data);
    }

    recalculateAllLevels(data) {
        const baseXP = parseInt(document.getElementById('edit_baseXP').value) || data.baseXP;
        const multiplier = parseFloat(document.getElementById('edit_multiplier').value) || data.multiplier;

        data.levels.forEach((level, index) => {
            const customValue = parseInt(document.getElementById(`edit_level_${index}_custom`).value) || 0;
            const calculated = baseXP * level.level * multiplier;
            const total = calculated + customValue;

            level.calculated = calculated;
            level.custom = customValue;
            level.total = total;

            this.updateLevelDisplay(index, calculated, customValue, total, baseXP, level.level, multiplier);
        });
    }

    recalculateLevel(data, index) {
        const baseXP = parseInt(document.getElementById('edit_baseXP').value) || data.baseXP;
        const multiplier = parseFloat(document.getElementById('edit_multiplier').value) || data.multiplier;
        const customValue = parseInt(document.getElementById(`edit_level_${index}_custom`).value) || 0;

        const level = data.levels[index];
        const calculated = baseXP * level.level * multiplier;
        const total = calculated + customValue;

        level.calculated = calculated;
        level.custom = customValue;
        level.total = total;

        this.updateLevelDisplay(index, calculated, customValue, total, baseXP, level.level, multiplier);
    }

    updateLevelDisplay(index, calculated, custom, total, baseXP, levelNum, multiplier) {
        const calculatedInput = document.getElementById(`edit_level_${index}_calculated`);
        const totalSpan = document.getElementById(`total_${index}`);
        const customDisplay = document.getElementById(`custom_display_${index}`);
        const resultSpan = document.getElementById(`result_${index}`);

        if (calculatedInput) calculatedInput.value = calculated;
        if (totalSpan) totalSpan.textContent = total;
        if (customDisplay) customDisplay.textContent = custom;
        if (resultSpan) resultSpan.textContent = total;

        // Aktualizovat výpočet text
        const calculatedInfo = document.querySelector(`#result_${index}`).closest('.calculated-info');
        if (calculatedInfo) {
            calculatedInfo.innerHTML = `
                <strong>Celkové XP pro level:</strong> <span id="total_${index}">${total}</span><br>
                <strong>Výpočet:</strong> ${baseXP} × ${levelNum} × ${multiplier} + <span id="custom_display_${index}">${custom}</span> = <span id="result_${index}">${total}</span>
            `;
        }
    }

    generateEditForm(card) {
        if (card.template === 'global-level') {
            return this.generateGlobalLevelEditForm(card.data);
        }
        
        return '<p>Editace pro tento typ karty není zatím implementována.</p>';
    }

    generateGlobalLevelEditForm(data) {
        let form = `
            <div class="form-group">
                <label>AFKB/min:</label>
                <input type="number" id="edit_afkbPerMin" value="${data.afkbPerMin}" min="0" step="0.1">
            </div>
            <div class="form-group">
                <label>AFKXP/min:</label>
                <input type="number" id="edit_afkxpPerMin" value="${data.afkxpPerMin}" min="0" step="0.1">
            </div>
            <div class="form-group">
                <label>Násobič pro další lvl:</label>
                <input type="number" id="edit_multiplier" value="${data.multiplier}" min="1" step="0.1">
            </div>
            <div class="form-group">
                <label>Základní hodnota AFKXP pro LVL:</label>
                <input type="number" id="edit_baseXP" value="${data.baseXP}" min="1">
            </div>
            <div class="form-group">
                <label>Počet úrovní:</label>
                <input type="number" id="edit_levelCount" value="${data.levels.length}" min="1" max="50">
                <button type="button" id="regenerateLevels" class="btn btn-secondary" style="margin-top: 0.5rem;">
                    Přegenerovat úrovně
                </button>
            </div>

            <h4 style="margin-top: 2rem; margin-bottom: 1rem;">Editace úrovní:</h4>
            <div class="levels-edit-container" style="max-height: 400px; overflow-y: auto;">
        `;

        // Přidat editovatelné pole pro každou úroveň
        data.levels.forEach((level, index) => {
            form += `
                <div class="level-edit-group" style="border: 1px solid #ddd; padding: 1rem; margin-bottom: 1rem; border-radius: 6px;">
                    <h5>Úroveň ${level.level}</h5>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label>Přidaná hodnota:</label>
                            <input type="number" id="edit_level_${index}_custom" value="${level.custom}" min="0">
                        </div>
                        <div class="form-group">
                            <label>Vypočítaná hodnota:</label>
                            <input type="number" id="edit_level_${index}_calculated" value="${level.calculated}" readonly style="background: #f5f5f5;">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Odměny (oddělené čárkou):</label>
                        <textarea id="edit_level_${index}_rewards" rows="2" style="width: 100%; resize: vertical;">${level.rewards}</textarea>
                    </div>
                    <div class="form-group">
                        <label>Poznámky:</label>
                        <input type="text" id="edit_level_${index}_notes" value="${level.notes}" placeholder="Volitelné poznámky">
                    </div>
                    <div class="calculated-info" style="background: #f8f9fa; padding: 0.5rem; border-radius: 4px; font-size: 0.9rem;">
                        <strong>Celkové XP pro level:</strong> <span id="total_${index}">${level.total}</span><br>
                        <strong>Výpočet:</strong> ${data.baseXP} × ${level.level} × ${data.multiplier} + <span id="custom_display_${index}">${level.custom}</span> = <span id="result_${index}">${level.total}</span>
                    </div>
                </div>
            `;
        });

        form += `
            </div>
            <button type="button" id="recalculateAll" class="btn btn-primary" style="margin-top: 1rem;">
                <i class="fas fa-calculator"></i> Přepočítat všechny úrovně
            </button>
        `;

        return form;
    }

    saveCardEdit() {
        if (!this.currentEditCard) return;
        
        const card = this.currentEditCard;
        
        if (card.template === 'global-level') {
            this.saveGlobalLevelEdit(card);
        }
        
        this.updateCardDisplay(card);
        document.getElementById('cardModal').style.display = 'none';
        this.currentEditCard = null;
    }

    saveGlobalLevelEdit(card) {
        const data = card.data;

        data.afkbPerMin = parseFloat(document.getElementById('edit_afkbPerMin').value);
        data.afkxpPerMin = parseFloat(document.getElementById('edit_afkxpPerMin').value);
        data.multiplier = parseFloat(document.getElementById('edit_multiplier').value);
        data.baseXP = parseInt(document.getElementById('edit_baseXP').value);

        // Uložit změny pro každou úroveň
        data.levels.forEach((level, index) => {
            const customInput = document.getElementById(`edit_level_${index}_custom`);
            const rewardsInput = document.getElementById(`edit_level_${index}_rewards`);
            const notesInput = document.getElementById(`edit_level_${index}_notes`);

            if (customInput) {
                level.custom = parseInt(customInput.value) || 0;
            }
            if (rewardsInput) {
                level.rewards = rewardsInput.value;
            }
            if (notesInput) {
                level.notes = notesInput.value;
            }

            // Přepočítat celkové XP podle vzorce: základní hodnota × úroveň × násobič + přidaná hodnota
            level.calculated = data.baseXP * level.level * data.multiplier;
            level.total = level.calculated + level.custom;
            level.time = level.total; // Čas potřebný k dosažení levelu
        });
    }

    updateCardDisplay(card) {
        const cardElement = document.getElementById(card.id);
        cardElement.innerHTML = this.generateCardHTML(card);
        this.setupCardEvents(cardElement, card);
    }

    generateYML() {
        if (this.cards.length === 0) {
            alert('Nejsou k dispozici žádné karty pro generování YML.');
            return;
        }
        
        const ymlContent = YMLGenerator.generateFromCards(this.cards);
        this.showYMLModal(ymlContent);
    }

    showYMLModal(content) {
        const modal = document.getElementById('ymlModal');
        const output = document.getElementById('ymlOutput');
        const textarea = document.getElementById('ymlTextarea');
        
        output.textContent = content;
        textarea.value = content;
        
        modal.style.display = 'block';
    }

    switchTab(tabName) {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`yml${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`).classList.add('active');
    }

    downloadYML() {
        const content = document.getElementById('ymlTextarea').value;
        const blob = new Blob([content], { type: 'text/yaml' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'afk-system.yml';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    copyYMLToClipboard() {
        const content = document.getElementById('ymlTextarea').value;
        navigator.clipboard.writeText(content).then(() => {
            alert('YML zkopírován do schránky!');
        });
    }

    showSimulation() {
        const modal = document.getElementById('simulationModal');
        modal.style.display = 'block';
    }

    runSimulation() {
        const simTime = parseInt(document.getElementById('simTime').value);
        const startLevel = parseInt(document.getElementById('startLevel').value);
        const startXP = parseInt(document.getElementById('startXP').value);
        
        const results = AFKSimulator.simulate(this.cards, simTime, startLevel, startXP);
        this.displaySimulationResults(results);
    }

    displaySimulationResults(results) {
        const container = document.getElementById('simulationResults');
        container.innerHTML = `
            <h4>Výsledky simulace</h4>
            <div class="result-item">
                <span class="result-label">Konečný level:</span>
                <span class="result-value">${results.finalLevel}</span>
            </div>
            <div class="result-item">
                <span class="result-label">Konečné XP:</span>
                <span class="result-value">${results.finalXP}</span>
            </div>
            <div class="result-item">
                <span class="result-label">Získané levely:</span>
                <span class="result-value">${results.levelsGained}</span>
            </div>
            <div class="result-item">
                <span class="result-label">Celkové XP získané:</span>
                <span class="result-value">${results.totalXPGained}</span>
            </div>
            <div class="result-item">
                <span class="result-label">Průměrné XP/min:</span>
                <span class="result-value">${results.avgXPPerMin.toFixed(2)}</span>
            </div>
        `;
    }

    exportAll() {
        const exportData = {
            cards: this.cards,
            timestamp: new Date().toISOString(),
            version: '1.0'
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'afk-editor-export.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    importYML(file) {
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const content = e.target.result;
                // Zde by byla logika pro parsování YML a vytvoření karet
                alert('Import YML bude implementován v další verzi.');
            } catch (error) {
                alert('Chyba při importu souboru: ' + error.message);
            }
        };
        reader.readAsText(file);
    }
}

// Inicializace editoru po načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    window.afkEditor = new AFKEditor();
});
